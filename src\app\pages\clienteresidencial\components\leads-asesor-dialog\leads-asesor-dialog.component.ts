import {
  Component,
  Inject,
  OnInit,
  OnDestroy,
  ViewChild,
  AfterViewInit,
  ChangeDetectorRef,
} from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { Subject, takeUntil } from 'rxjs';
import { Store } from '@ngrx/store';
import { HttpClient } from '@angular/common/http';
import { EstadisticasSedeService } from '../../services/estadisticas-sede.service';
import { ClienteConUsuarioDTO } from '@app/models/backend/clienteresidencial';
import { environment } from '@src/environments/environment';
import * as fromClienteActions from '../../store/save/save.actions';
import Swal from 'sweetalert2';

export interface LeadsAsesorDialogData {
  nombreAsesor: string;
  fecha: string;
  fechaFin?: string; // Fecha fin opcional para rangos de fechas
  onVerDetalles?: (lead: ClienteConUsuarioDTO) => void;
}

@Component({
  selector: 'app-leads-asesor-dialog',
  templateUrl: './leads-asesor-dialog.component.html',
  styleUrls: ['./leads-asesor-dialog.component.scss'],
})
export class LeadsAsesorDialogComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  private destroy$ = new Subject<void>();

  // ViewChild para el paginador
  @ViewChild('leadsPaginator') paginator!: MatPaginator;

  // Datos
  leads: ClienteConUsuarioDTO[] = [];
  loading = false;

  // Búsqueda
  busquedaMovil = '';

  // Paginación
  currentPage = 0;
  pageSize = 10;
  totalPages = 0;
  totalElements = 0;
  hasNext = false;
  hasPrevious = false;
  pageSizeOptions = [5, 10, 20, 50];

  constructor(
    public dialogRef: MatDialogRef<LeadsAsesorDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: LeadsAsesorDialogData,
    private estadisticasSedeService: EstadisticasSedeService,
    private store: Store,
    private http: HttpClient,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.cargarLeads();
  }

  ngAfterViewInit(): void {
    // Sincronizar el paginador después de que se inicialice la vista
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Carga los leads del asesor
   */
  cargarLeads(): void {
    this.loading = true;

    console.log('=== CARGANDO LEADS DEL ASESOR ===');
    console.log('Asesor:', this.data.nombreAsesor);
    console.log('Fecha:', this.data.fecha);
    console.log('Fecha Fin:', this.data.fechaFin);
    console.log('Búsqueda Móvil:', this.busquedaMovil);
    console.log('Página:', this.currentPage);
    console.log('Tamaño:', this.pageSize);

    // Detectar si es rango de fechas o fecha específica
    const esRangoFechas =
      this.data.fechaFin && this.data.fechaFin !== this.data.fecha;
    console.log('Es rango de fechas:', esRangoFechas);

    const serviceCall = esRangoFechas
      ? this.estadisticasSedeService.obtenerLeadsPorAsesorYRangoFechasConBusqueda(
          this.data.nombreAsesor,
          this.data.fecha,
          this.data.fechaFin!,
          this.busquedaMovil,
          this.currentPage,
          this.pageSize
        )
      : this.estadisticasSedeService.obtenerLeadsPorAsesorYFechaConBusqueda(
          this.data.nombreAsesor,
          this.data.fecha,
          this.busquedaMovil,
          this.currentPage,
          this.pageSize
        );

    serviceCall.pipe(takeUntil(this.destroy$)).subscribe({
      next: (response) => {
        console.log('Respuesta del backend (leads):', response);
        if (response.rpta === 1 && response.data) {
          // Convertir los datos a ClienteConUsuarioDTO si es necesario
          this.leads = (response.data.clientes || []).map(
            (cliente: any) => new ClienteConUsuarioDTO(cliente)
          );
          this.currentPage = response.data.currentPage || 0;
          this.totalPages = response.data.totalPages || 0;
          this.totalElements = response.data.totalItems || 0;
          this.hasNext = this.currentPage < this.totalPages - 1;
          this.hasPrevious = this.currentPage > 0;

          console.log(
            'Leads cargados - Página:',
            this.currentPage,
            'Total páginas:',
            this.totalPages,
            'Total elementos:',
            this.totalElements
          );
        } else {
          this.resetearDatos();
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error al cargar leads del asesor:', error);
        this.resetearDatos();
        this.loading = false;
      },
    });
  }

  /**
   * Método para manejar eventos de paginación de mat-paginator
   */
  handlePageEvent(event: { pageIndex: number; pageSize: number }): void {
    console.log('=== HANDLE PAGE EVENT (LEADS) ===');
    console.log(
      'event.pageIndex:',
      event.pageIndex,
      'event.pageSize:',
      event.pageSize
    );
    console.log('currentPage ANTES:', this.currentPage);

    // Solo proceder si realmente cambió la página
    if (
      this.currentPage !== event.pageIndex ||
      this.pageSize !== event.pageSize
    ) {
      console.log('Página cambió de', this.currentPage, 'a', event.pageIndex);

      // Actualizar las propiedades
      this.currentPage = event.pageIndex;
      this.pageSize = event.pageSize;

      console.log('currentPage DESPUÉS:', this.currentPage);

      // Cargar leads con los nuevos valores
      this.cargarLeads();
    } else {
      console.log('Página no cambió, ignorando evento');
    }
  }

  /**
   * Maneja el cambio de página (método legacy para compatibilidad)
   */
  onPageChange(page: number): void {
    if (page >= 0 && page < this.totalPages) {
      this.currentPage = page;
      this.cargarLeads();
    }
  }

  /**
   * Maneja el cambio de tamaño de página (método legacy para compatibilidad)
   */
  onPageSizeChange(newSize: number): void {
    this.pageSize = newSize;
    this.currentPage = 0;
    this.cargarLeads();
  }

  /**
   * Resetea los datos
   */
  private resetearDatos(): void {
    this.leads = [];
    this.currentPage = 0;
    this.totalPages = 0;
    this.totalElements = 0;
    this.hasNext = false;
    this.hasPrevious = false;
  }

  /**
   * Cierra el diálogo
   */
  cerrarDialog(): void {
    this.dialogRef.close();
  }

  /**
   * Carga los detalles del cliente
   */
  verDetalles(lead: ClienteConUsuarioDTO): void {
    if (!lead.dni || !lead.numeroMovil) {
      Swal.fire('Error', 'Datos del cliente incompletos', 'error');
      return;
    }

    // Cerrar el diálogo con un resultado que indique que se abrió el modal de detalles
    this.dialogRef.close('verDetalles');

    // Si hay un callback proporcionado por el componente padre, usarlo
    if (this.data.onVerDetalles) {
      this.data.onVerDetalles(lead);
    } else {
      // Fallback: usar el store directamente
      const fechaFormateada = lead.getFechaCreacionFormatted();

      if (fechaFormateada) {
        this.store.dispatch(
          fromClienteActions.loadClienteDetalle({
            dni: lead.dni,
            mobile: lead.numeroMovil,
            fechaCreacion: fechaFormateada,
          })
        );
      } else {
        // Si la fecha no es válida, enviar una cadena vacía
        this.store.dispatch(
          fromClienteActions.loadClienteDetalle({
            dni: lead.dni,
            mobile: lead.numeroMovil,
            fechaCreacion: '',
          })
        );
      }
    }

    // No se realiza ninguna navegación aquí, ya que queremos mantener al usuario
    // en la página actual (/clienteresidencial/estadisticas)
  }

  /**
   * Descarga los datos del cliente en formato Excel
   */
  descargarExcel(lead: ClienteConUsuarioDTO): void {
    if (!lead.numeroMovil) {
      Swal.fire(
        'Error',
        'No se pudo identificar el número móvil del cliente',
        'error'
      );
      return;
    }

    // Mostrar indicador de carga
    Swal.fire({
      title: 'Generando Excel...',
      text: 'Por favor espere mientras se genera el archivo',
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      },
    });

    const url = `${environment.url}api/clientes/exportar-excel-individual/${lead.numeroMovil}`;

    this.http.get(url, { responseType: 'blob' }).subscribe({
      next: (blob) => {
        // Verificar si el blob es un JSON de error
        if (blob.type === 'application/json') {
          const reader = new FileReader();
          reader.onload = () => {
            try {
              const errorJson = JSON.parse(reader.result as string);
              console.error('Error del servidor:', errorJson);
              Swal.fire(
                'Error',
                errorJson.detail || 'Error al generar el Excel',
                'error'
              );
            } catch (e) {
              Swal.fire(
                'Error',
                'No se pudo procesar la respuesta del servidor',
                'error'
              );
            }
          };
          reader.readAsText(blob);
          return;
        }

        // Descargar el archivo
        this.downloadFile(blob, `Cliente_${lead.dni}_${lead.numeroMovil}.xlsx`);
        Swal.fire(
          'Éxito',
          'El archivo Excel se ha descargado correctamente',
          'success'
        );
      },
      error: (error) => {
        console.error('Error al descargar Excel:', error);
        Swal.fire('Error', 'No se pudo descargar el archivo Excel', 'error');
      },
    });
  }

  /**
   * Descarga un archivo blob
   */
  private downloadFile(blob: Blob, fileName: string): void {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.click();
    window.URL.revokeObjectURL(url);
  }

  /**
   * Busca leads por número móvil
   */
  buscarPorMovil(): void {
    console.log('=== BUSCAR POR MÓVIL ===');
    console.log('Término de búsqueda:', this.busquedaMovil);

    // Resetear la paginación al buscar
    this.currentPage = 0;

    // Cargar leads con el filtro de búsqueda
    this.cargarLeads();
  }

  /**
   * Limpia la búsqueda y recarga todos los leads
   */
  limpiarBusqueda(): void {
    console.log('=== LIMPIAR BÚSQUEDA ===');

    // Limpiar el término de búsqueda
    this.busquedaMovil = '';

    // Resetear la paginación
    this.currentPage = 0;

    // Cargar todos los leads sin filtro
    this.cargarLeads();
  }
}
