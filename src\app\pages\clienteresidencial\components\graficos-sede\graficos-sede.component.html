<div class="dark:bg-gray-900 shadow-md rounded-2xl p-6 sm:p-8 transition-all">
    <div
        class="mb-3"
        *ngIf="
          fechaFinControl.value &&
          fechaFinControl.value !== fechaInicioControl.value
        "
      >
        <div
          class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3"
        >
          <div class="flex items-center">
            <svg
              class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clip-rule="evenodd"
              ></path>
            </svg>
            <span class="text-sm font-medium text-blue-800 dark:text-blue-200">
              Mostrando estadísticas acumuladas del
              {{ fechaInicioControl.value | date : "dd/MM/yyyy" }} al
              {{ fechaFinControl.value | date : "dd/MM/yyyy" }}
            </span>
          </div>
        </div>
      </div>
  <!-- Cabecera -->
  <div
    class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6"
  >
   <div class="flex flex-col">
    <label
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >Buscar Vendedor</label
        >
    <div class="relative w-full md:w-80">

        <input
          type="text"
          [formControl]="buscarVendedorControl"
          placeholder="Buscar por nombre de vendedor..."
          class="w-full px-3 py-2 pl-10 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
        />
        <svg
          class="w-5 h-5 text-gray-400 dark:text-gray-500 absolute left-3 top-1/2 transform -translate-y-1/2"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          ></path>
        </svg>
        <!-- Botón para limpiar búsqueda -->
        <button
          *ngIf="buscarVendedorControl.value"
          (click)="limpiarBusquedaVendedor()"
          class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
          title="Limpiar búsqueda"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            ></path>
          </svg>
        </button>
      </div>

      <!-- Contador de resultados -->
      <p
        *ngIf="buscarVendedorControl.value && estadisticasFiltradas.length !== estadisticas.length"
        class="text-xs text-gray-500 dark:text-gray-400 mt-1"
      >
        Mostrando {{ estadisticasFiltradas.length }} de {{ estadisticas.length }} registros
      </p>

  
    </div>


    <!-- Controles de filtro -->
    <div
      class="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4 w-full sm:w-auto"
    >
      <!-- Selector de Sede -->
      <div class="w-full sm:w-48">
        <label
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >Sede</label
        >
        <select
          [formControl]="sedeControl"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
        >
          <option value="todas">Todas las sedes</option>
          <option *ngFor="let sede of sedes" [value]="sede.id">
            {{ sede.nombre }}
          </option>
        </select>
      </div>

      <!-- Selector de Supervisor -->
      <div class="w-full sm:w-48">
        <label
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >Supervisor</label
        >
        <select
          [formControl]="supervisorControl"
          [disabled]="
            sedeControl.value === 'todas' || supervisores.length === 0
          "
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white disabled:bg-gray-100 disabled:text-gray-400 dark:disabled:bg-gray-700 dark:disabled:text-gray-500"
        >
          <option value="todos">Todos los supervisores</option>
          <option
            *ngFor="let supervisor of supervisores"
            [value]="supervisor.id"
          >
            {{ supervisor.nombre }}
          </option>
        </select>
      </div>

      <!-- Campo Fecha Inicio -->
      <div class="w-full sm:w-48">
        <label
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >Fecha Inicio</label
        >
        <input
          type="date"
          [formControl]="fechaInicioControl"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
        />
      </div>

      <!-- Campo Fecha Fin -->
      <div class="w-full sm:w-48">
        <label
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >Fecha Fin</label
        >
        <input
          type="date"
          [formControl]="fechaFinControl"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
        />
      </div>

      <!-- Botón Exportar por Fecha -->
      <div class="w-full sm:w-auto">
        <label class="block text-sm font-medium text-transparent mb-1"
          >&nbsp;</label
        >
        <button
          (click)="exportarPorFecha()"
          [disabled]="loading || exportLoading"
          class="w-full sm:w-auto px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white text-sm font-medium rounded-md shadow-sm transition-colors duration-200 flex items-center justify-center gap-2"
          title="Exportar leads de la fecha específica a Excel"
        >
          <svg
            *ngIf="!exportLoading"
            class="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            ></path>
          </svg>
          <div
            *ngIf="exportLoading"
            class="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"
          ></div>
          <span>{{
            exportLoading ? "Exportando..." : "Exportar por Fecha"
          }}</span>
        </button>
      </div>

      <!-- Botón Exportar por Rango de Fechas -->
      <div class="w-full sm:w-auto">
        <label class="block text-sm font-medium text-transparent mb-1"
          >&nbsp;</label
        >
        <button
          (click)="exportarPorRangoFechas()"
          [disabled]="
            loading ||
            exportRangoLoading ||
            !fechaInicioControl.value ||
            !fechaFinControl.value
          "
          class="w-full sm:w-auto px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white text-sm font-medium rounded-md shadow-sm transition-colors duration-200 flex items-center justify-center gap-2"
          title="Exportar leads del rango de fechas a Excel"
        >
          <svg
            *ngIf="!exportRangoLoading"
            class="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
            ></path>
          </svg>
          <div
            *ngIf="exportRangoLoading"
            class="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"
          ></div>
          <span>{{
            exportRangoLoading ? "Exportando..." : "Exportar por Rango"
          }}</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Indicador de carga mejorado -->
  <div
    *ngIf="loading"
    class="flex flex-col items-center justify-center p-12 bg-gray-50 dark:bg-gray-800 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg"
  >
    <div class="relative">
      <div
        class="animate-spin rounded-full h-12 w-12 border-4 border-gray-200 dark:border-gray-700"
      ></div>
      <div
        class="animate-spin rounded-full h-12 w-12 border-4 border-blue-600 border-t-transparent absolute top-0 left-0"
      ></div>
    </div>
    <div class="mt-4 text-center">
      <p class="text-gray-600 dark:text-gray-400 font-medium">
        Cargando estadísticas...
      </p>
      <p class="text-sm text-gray-500 dark:text-gray-500 mt-1">
        Página {{ currentPage + 1 }} • {{ pageSize }} registros por página
      </p>
    </div>
  </div>

  <!-- Tabla de estadísticas -->
  <div *ngIf="!loading" class="overflow-x-auto rounded-xl shadow">
    <table
      class="min-w-full border border-gray-200 dark:border-gray-700 text-sm text-left text-gray-700 dark:text-white"
    >
      <thead>
        <tr>
          <th
            class="px-6 py-3 bg-green-500 dark:bg-green-700 text-white uppercase text-xs font-semibold"
          >
            Sede
          </th>
          <th
            class="px-6 py-3 bg-green-500 dark:bg-green-700 text-white uppercase text-xs font-semibold"
          >
            Supervisor
          </th>
          <th
            class="px-6 py-3 bg-green-500 dark:bg-green-700 text-white uppercase text-xs font-semibold"
          >
            Vendedor
          </th>
          <th
            class="px-6 py-3 bg-green-500 dark:bg-green-700 text-white uppercase text-xs font-semibold text-center"
          >
            Toma de Datos
          </th>
          <th
            class="px-6 py-3 bg-green-500 dark:bg-green-700 text-white uppercase text-xs font-semibold text-center"
          >
            Interesados en Seguro
          </th>
          <th
            class="px-6 py-3 bg-green-500 dark:bg-green-700 text-white uppercase text-xs font-semibold text-center"
          >
            Interesados en Energía
          </th>
          <th
            class="px-6 py-3 bg-green-500 dark:bg-green-700 text-white uppercase text-xs font-semibold text-center"
          >
            Interesados en Lowi
          </th>
          <th
            class="px-6 py-3 bg-green-500 dark:bg-green-700 text-white uppercase text-xs font-semibold text-center"
          >
            Acciones
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          *ngFor="let estadistica of estadisticasFiltradas"
          class="hover:bg-gray-50 dark:hover:bg-gray-800"
        >
          <td
            class="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 font-medium"
          >
            {{ estadistica.sede }}
          </td>
          <td
            class="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700"
          >
            {{ estadistica.supervisor || "-" }}
          </td>
          <td
            class="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700"
          >
            {{ estadistica.vendedor }}
          </td>
          <td
            class="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 text-center font-bold text-blue-600"
          >
            {{ estadistica.tomaDatos }}
          </td>
          <td
            class="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 text-center font-bold text-green-600"
          >
            {{ estadistica.interesadosSeguro }}
          </td>
          <td
            class="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 text-center font-bold text-orange-600"
          >
            {{ estadistica.interesadosEnergia }}
          </td>
          <td
            class="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 text-center font-bold text-purple-600"
          >
            {{ estadistica.interesadosLowi }}
          </td>
          <td
            class="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 text-center"
          >
            <button
              (click)="verLeadsAsesor(estadistica)"
              class="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white text-xs rounded-md transition-colors duration-200 flex items-center gap-1 mx-auto"
              title="Ver leads del asesor"
            >
              <svg
                class="w-3 h-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                ></path>
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                ></path>
              </svg>
              Ver Leads
            </button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Paginación inferior -->
  <mat-paginator
    #estadisticasPaginator
    *ngIf="!loading && estadisticas && estadisticas.length > 0"
    class="bg-white dark:bg-gray-900 border-t-0 border border-gray-200 dark:border-gray-700"
    [length]="totalElements"
    [pageIndex]="currentPage"
    [pageSize]="pageSize"
    [pageSizeOptions]="pageSizeOptions"
    [showFirstLastButtons]="true"
    (page)="handlePageEvent($event)"
  >
  </mat-paginator>

  <!-- Estado vacío -->
  <div
    *ngIf="!loading && estadisticas?.length === 0"
    class="flex flex-col items-center justify-center p-12 text-center bg-gray-50 dark:bg-gray-800 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg mt-6"
  >
    <div class="text-5xl text-gray-400 dark:text-gray-600 mb-4">📊</div>
    <p class="text-gray-600 dark:text-gray-400">
      No hay estadísticas disponibles para los filtros seleccionados
    </p>
  </div>
</div>
