package com.midas.crm.service;

import com.midas.crm.entity.DTO.EstadisticaSedeDTO;
import com.midas.crm.entity.DTO.EstadisticaSedePaginadaResponse;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * Servicio para manejar las estadísticas por sede
 */
public interface EstadisticasSedeService {

    /**
     * Obtiene estadísticas agrupadas por sede, supervisor y vendedor para una fecha
     * específica
     *
     * @param sedeId ID de la sede (opcional, null para todas las sedes)
     * @param fecha  Fecha para filtrar
     * @return Lista de estadísticas
     */
    List<EstadisticaSedeDTO> obtenerEstadisticasPorSede(Long sedeId, LocalDate fecha);

    /**
     * Obtiene estadísticas resumidas por sede para una fecha específica
     *
     * @param fecha Fecha para filtrar
     * @return Lista de estadísticas resumidas
     */
    List<EstadisticaSedeDTO> obtenerResumenPorSede(LocalDate fecha);

    /**
     * Obtiene estadísticas por rango de fechas
     *
     * @param fechaInicio Fecha de inicio
     * @param fechaFin    Fecha de fin
     * @param sedeId      ID de la sede (opcional)
     * @return Lista de estadísticas
     */
    List<EstadisticaSedeDTO> obtenerEstadisticasPorRango(LocalDate fechaInicio, LocalDate fechaFin, Long sedeId);

    // ===== MÉTODOS PAGINADOS =====

    /**
     * Obtiene estadísticas agrupadas por sede, supervisor y vendedor para una fecha
     * específica con paginación
     *
     * @param sedeId       ID de la sede (opcional, null para todas las sedes)
     * @param supervisorId ID del supervisor (opcional, null para todos los
     *                     supervisores)
     * @param fecha        Fecha para filtrar
     * @param pageable     Información de paginación
     * @return Respuesta paginada con estadísticas
     */
    EstadisticaSedePaginadaResponse obtenerEstadisticasPorSedePaginado(Long sedeId, Long supervisorId,
                                                                       LocalDate fecha,
                                                                       Pageable pageable);

    /**
     * Obtiene estadísticas por rango de fechas con paginación
     *
     * @param fechaInicio Fecha de inicio
     * @param fechaFin    Fecha de fin
     * @param sedeId      ID de la sede (opcional)
     * @param pageable    Información de paginación
     * @return Respuesta paginada con estadísticas
     */
    EstadisticaSedePaginadaResponse obtenerEstadisticasPorRangoPaginado(LocalDate fechaInicio, LocalDate fechaFin,
                                                                        Long sedeId, Pageable pageable);

    /**
     * Obtiene leads específicos de un asesor para una fecha determinada
     *
     * @param nombreAsesor Nombre completo del asesor
     * @param fecha        Fecha para filtrar
     * @param pageable     Información de paginación
     * @return Respuesta paginada con los leads del asesor
     */
    Map<String, Object> obtenerLeadsPorAsesorYFecha(String nombreAsesor, LocalDate fecha, Pageable pageable);

    /**
     * Obtiene supervisores/coordinadores por sede
     *
     * @param sedeId ID de la sede
     * @return Lista de supervisores de la sede
     */
    List<Map<String, Object>> obtenerSupervisoresPorSede(Long sedeId);

    /**
     * Exporta a Excel todos los leads filtrados por sede, supervisor y fecha
     * específica
     *
     * @param sedeId       ID de la sede (opcional)
     * @param supervisorId ID del supervisor (opcional)
     * @param fecha        Fecha para filtrar
     * @return Array de bytes con el archivo Excel generado
     */
    byte[] exportarLeadsPorRango(Long sedeId, Long supervisorId, LocalDate fecha);

    /**
     * Obtiene estadísticas acumuladas por rango de fechas con paginación
     *
     * @param sedeId       ID de la sede (opcional)
     * @param supervisorId ID del supervisor (opcional)
     * @param fechaInicio  Fecha de inicio del rango
     * @param fechaFin     Fecha de fin del rango
     * @param pageable     Información de paginación
     * @return Estadísticas acumuladas paginadas
     */
    EstadisticaSedePaginadaResponse obtenerEstadisticasPorRangoFechas(Long sedeId, Long supervisorId,
                                                                      LocalDate fechaInicio, LocalDate fechaFin, Pageable pageable);

    /**
     * Exporta a Excel todos los leads filtrados por sede, supervisor y rango de
     * fechas
     *
     * @param sedeId       ID de la sede (opcional)
     * @param supervisorId ID del supervisor (opcional)
     * @param fechaInicio  Fecha de inicio del rango
     * @param fechaFin     Fecha de fin del rango
     * @return Array de bytes con el archivo Excel generado
     */
    byte[] exportarLeadsPorRangoFechas(Long sedeId, Long supervisorId, LocalDate fechaInicio, LocalDate fechaFin);
}
