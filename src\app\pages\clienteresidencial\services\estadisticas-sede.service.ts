import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '@src/environments/environment';
import { GenericResponse } from '@app/models/backend/generic-response';

export interface EstadisticaSede {
  sede: string;
  supervisor: string;
  vendedor: string;
  tomaDatos: number;
  interesadosSeguro: number;
  interesadosEnergia: number;
  interesadosLowi: number;
}

export interface EstadisticasSedeResponse {
  estadisticas: EstadisticaSede[];
  totalRegistros: number;
  fecha: string;
}

export interface EstadisticaSedePaginadaResponse {
  estadisticas: EstadisticaSede[];
  currentPage: number;
  totalPages: number;
  totalElements: number;
  pageSize: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

@Injectable({
  providedIn: 'root',
})
export class EstadisticasSedeService {
  private baseUrl = `${environment.url}api/estadisticas-sede`;

  constructor(private http: HttpClient) {}

  /**
   * Obtiene estadísticas agrupadas por sede, supervisor y vendedor
   * @param sedeId ID de la sede (opcional, null para todas)
   * @param fecha Fecha para filtrar (formato YYYY-MM-DD)
   * @returns Observable con las estadísticas
   */
  obtenerEstadisticasPorSede(
    sedeId: number | null,
    fecha: string
  ): Observable<GenericResponse<EstadisticaSede[]>> {
    // Llamada real al backend
    let params = new HttpParams();

    if (sedeId !== null && sedeId !== undefined) {
      params = params.set('sedeId', sedeId.toString());
    }

    if (fecha) {
      params = params.set('fecha', fecha);
    }

    return this.http.get<GenericResponse<EstadisticaSede[]>>(this.baseUrl, {
      params,
    });
  }

  /**
   * Obtiene estadísticas resumidas por sede
   * @param fecha Fecha para filtrar (formato YYYY-MM-DD)
   * @returns Observable con estadísticas resumidas
   */
  obtenerResumenPorSede(fecha: string): Observable<GenericResponse<any[]>> {
    let params = new HttpParams();

    if (fecha) {
      params = params.set('fecha', fecha);
    }

    return this.http.get<GenericResponse<any[]>>(`${this.baseUrl}/resumen`, {
      params,
    });
  }

  /**
   * Obtiene estadísticas de un coordinador específico
   * @param coordinadorId ID del coordinador
   * @param fecha Fecha para filtrar (formato YYYY-MM-DD)
   * @returns Observable con estadísticas del coordinador
   */
  obtenerEstadisticasPorCoordinador(
    coordinadorId: number,
    fecha: string
  ): Observable<GenericResponse<any[]>> {
    let params = new HttpParams();

    if (fecha) {
      params = params.set('fecha', fecha);
    }

    return this.http.get<GenericResponse<any[]>>(
      `${this.baseUrl}/coordinador/${coordinadorId}`,
      { params }
    );
  }

  /**
   * Obtiene estadísticas de registros por rango de fechas
   * @param fechaInicio Fecha de inicio (formato YYYY-MM-DD)
   * @param fechaFin Fecha de fin (formato YYYY-MM-DD)
   * @param sedeId ID de la sede (opcional)
   * @returns Observable con estadísticas por rango
   */
  obtenerEstadisticasPorRango(
    fechaInicio: string,
    fechaFin: string,
    sedeId?: number
  ): Observable<GenericResponse<any[]>> {
    let params = new HttpParams()
      .set('fechaInicio', fechaInicio)
      .set('fechaFin', fechaFin);

    if (sedeId) {
      params = params.set('sedeId', sedeId.toString());
    }

    return this.http.get<GenericResponse<any[]>>(`${this.baseUrl}/rango`, {
      params,
    });
  }

  // ===== MÉTODOS PAGINADOS =====

  /**
   * Obtiene estadísticas agrupadas por sede con paginación
   * @param sedeId ID de la sede (opcional, null para todas)
   * @param supervisorId ID del supervisor (opcional, null para todos)
   * @param fecha Fecha para filtrar (formato YYYY-MM-DD)
   * @param page Número de página (por defecto 0)
   * @param size Tamaño de página (por defecto 10)
   * @returns Observable con las estadísticas paginadas
   */
  obtenerEstadisticasPorSedePaginado(
    sedeId: number | null,
    supervisorId: number | null,
    fecha: string,
    page: number = 0,
    size: number = 10
  ): Observable<GenericResponse<EstadisticaSedePaginadaResponse>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    if (sedeId !== null && sedeId !== undefined) {
      params = params.set('sedeId', sedeId.toString());
    }

    if (supervisorId !== null && supervisorId !== undefined) {
      params = params.set('supervisorId', supervisorId.toString());
    }

    if (fecha) {
      params = params.set('fecha', fecha);
    }

    return this.http.get<GenericResponse<EstadisticaSedePaginadaResponse>>(
      `${this.baseUrl}/paginado`,
      { params }
    );
  }

  /**
   * Obtiene estadísticas agrupadas por sede con paginación y filtro de búsqueda por vendedor
   * @param sedeId ID de la sede (opcional, null para todas)
   * @param supervisorId ID del supervisor (opcional, null para todos)
   * @param fecha Fecha para filtrar (formato YYYY-MM-DD)
   * @param busquedaVendedor Término de búsqueda para el nombre del vendedor (opcional)
   * @param page Número de página (por defecto 0)
   * @param size Tamaño de página (por defecto 10)
   * @returns Observable con las estadísticas paginadas
   */
  obtenerEstadisticasPorSedePaginadoConBusqueda(
    sedeId: number | null,
    supervisorId: number | null,
    fecha: string,
    busquedaVendedor: string | null,
    page: number = 0,
    size: number = 10
  ): Observable<GenericResponse<EstadisticaSedePaginadaResponse>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    if (sedeId !== null && sedeId !== undefined) {
      params = params.set('sedeId', sedeId.toString());
    }

    if (supervisorId !== null && supervisorId !== undefined) {
      params = params.set('supervisorId', supervisorId.toString());
    }

    if (fecha) {
      params = params.set('fecha', fecha);
    }

    if (busquedaVendedor && busquedaVendedor.trim() !== '') {
      params = params.set('busquedaVendedor', busquedaVendedor.trim());
    }

    console.log('Obteniendo estadísticas paginadas con búsqueda:', {
      sedeId,
      supervisorId,
      fecha,
      busquedaVendedor,
      page,
      size,
    });

    return this.http.get<GenericResponse<EstadisticaSedePaginadaResponse>>(
      `${this.baseUrl}/paginado/busqueda`,
      { params }
    );
  }

  /**
   * Obtiene estadísticas por rango de fechas con paginación
   * @param fechaInicio Fecha de inicio (formato YYYY-MM-DD)
   * @param fechaFin Fecha de fin (formato YYYY-MM-DD)
   * @param sedeId ID de la sede (opcional)
   * @param page Número de página (por defecto 0)
   * @param size Tamaño de página (por defecto 10)
   * @returns Observable con estadísticas paginadas por rango
   */
  obtenerEstadisticasPorRangoPaginado(
    fechaInicio: string,
    fechaFin: string,
    sedeId?: number,
    page: number = 0,
    size: number = 10
  ): Observable<GenericResponse<EstadisticaSedePaginadaResponse>> {
    let params = new HttpParams()
      .set('fechaInicio', fechaInicio)
      .set('fechaFin', fechaFin)
      .set('page', page.toString())
      .set('size', size.toString());

    if (sedeId) {
      params = params.set('sedeId', sedeId.toString());
    }

    return this.http.get<GenericResponse<EstadisticaSedePaginadaResponse>>(
      `${this.baseUrl}/rango/paginado`,
      { params }
    );
  }

  /**
   * Obtiene estadísticas acumuladas por rango de fechas con paginación
   * @param sedeId ID de la sede (opcional, null para todas)
   * @param supervisorId ID del supervisor (opcional, null para todos)
   * @param fechaInicio Fecha de inicio del rango (formato YYYY-MM-DD)
   * @param fechaFin Fecha de fin del rango (formato YYYY-MM-DD)
   * @param page Número de página (por defecto 0)
   * @param size Tamaño de página (por defecto 10)
   * @returns Observable con estadísticas acumuladas paginadas
   */
  obtenerEstadisticasPorRangoFechas(
    sedeId: number | null,
    supervisorId: number | null,
    fechaInicio: string,
    fechaFin: string,
    page: number = 0,
    size: number = 10
  ): Observable<GenericResponse<EstadisticaSedePaginadaResponse>> {
    let params = new HttpParams()
      .set('fechaInicio', fechaInicio)
      .set('fechaFin', fechaFin)
      .set('page', page.toString())
      .set('size', size.toString());

    if (sedeId !== null && sedeId !== undefined) {
      params = params.set('sedeId', sedeId.toString());
    }

    if (supervisorId !== null && supervisorId !== undefined) {
      params = params.set('supervisorId', supervisorId.toString());
    }

    console.log('Obteniendo estadísticas acumuladas por rango de fechas:', {
      sedeId,
      supervisorId,
      fechaInicio,
      fechaFin,
      page,
      size,
    });

    return this.http.get<GenericResponse<EstadisticaSedePaginadaResponse>>(
      `${this.baseUrl}/rango-fechas/paginado`,
      { params }
    );
  }

  /**
   * Obtiene estadísticas acumuladas por rango de fechas con paginación y búsqueda por vendedor
   * @param sedeId ID de la sede (opcional, null para todas)
   * @param supervisorId ID del supervisor (opcional, null para todos)
   * @param fechaInicio Fecha de inicio del rango (formato YYYY-MM-DD)
   * @param fechaFin Fecha de fin del rango (formato YYYY-MM-DD)
   * @param busquedaVendedor Término de búsqueda para el nombre del vendedor (opcional)
   * @param page Número de página (por defecto 0)
   * @param size Tamaño de página (por defecto 10)
   * @returns Observable con estadísticas acumuladas paginadas con búsqueda
   */
  obtenerEstadisticasPorRangoFechasConBusqueda(
    sedeId: number | null,
    supervisorId: number | null,
    fechaInicio: string,
    fechaFin: string,
    busquedaVendedor: string | null,
    page: number = 0,
    size: number = 10
  ): Observable<GenericResponse<EstadisticaSedePaginadaResponse>> {
    let params = new HttpParams()
      .set('fechaInicio', fechaInicio)
      .set('fechaFin', fechaFin)
      .set('page', page.toString())
      .set('size', size.toString());

    if (sedeId !== null && sedeId !== undefined) {
      params = params.set('sedeId', sedeId.toString());
    }

    if (supervisorId !== null && supervisorId !== undefined) {
      params = params.set('supervisorId', supervisorId.toString());
    }

    if (busquedaVendedor && busquedaVendedor.trim() !== '') {
      params = params.set('busquedaVendedor', busquedaVendedor.trim());
    }

    console.log(
      'Obteniendo estadísticas acumuladas por rango de fechas con búsqueda:',
      {
        sedeId,
        supervisorId,
        fechaInicio,
        fechaFin,
        busquedaVendedor,
        page,
        size,
      }
    );

    return this.http.get<GenericResponse<EstadisticaSedePaginadaResponse>>(
      `${this.baseUrl}/rango-fechas/paginado/busqueda`,
      { params }
    );
  }

  /**
   * Obtiene leads específicos de un asesor para una fecha determinada
   * @param nombreAsesor Nombre completo del asesor
   * @param fecha Fecha para filtrar (formato YYYY-MM-DD)
   * @param page Número de página (por defecto 0)
   * @param size Tamaño de página (por defecto 10)
   * @returns Observable con los leads del asesor paginados
   */
  obtenerLeadsPorAsesorYFecha(
    nombreAsesor: string,
    fecha: string,
    page: number = 0,
    size: number = 10
  ): Observable<GenericResponse<any>> {
    let params = new HttpParams()
      .set('nombreAsesor', nombreAsesor)
      .set('fecha', fecha)
      .set('page', page.toString())
      .set('size', size.toString());

    return this.http.get<GenericResponse<any>>(`${this.baseUrl}/leads-asesor`, {
      params,
    });
  }

  /**
   * Obtiene supervisores/coordinadores por sede
   * @param sedeId ID de la sede
   * @returns Observable con la lista de supervisores
   */
  obtenerSupervisoresPorSede(
    sedeId: number
  ): Observable<GenericResponse<any[]>> {
    let params = new HttpParams().set('sedeId', sedeId.toString());

    return this.http.get<GenericResponse<any[]>>(
      `${this.baseUrl}/supervisores-por-sede`,
      {
        params,
      }
    );
  }

  /**
   * Exporta a Excel todos los leads filtrados por sede, supervisor y fecha específica
   * @param sedeId ID de la sede (opcional)
   * @param supervisorId ID del supervisor (opcional)
   * @param fecha Fecha para filtrar (formato YYYY-MM-DD)
   * @returns Observable con el archivo Excel como blob
   */
  exportarLeadsPorFecha(
    sedeId: number | null,
    supervisorId: number | null,
    fecha: string
  ): Observable<Blob> {
    let params = new HttpParams().set('fecha', fecha);

    if (sedeId !== null && sedeId !== undefined) {
      params = params.set('sedeId', sedeId.toString());
    }

    if (supervisorId !== null && supervisorId !== undefined) {
      params = params.set('supervisorId', supervisorId.toString());
    }

    return this.http.get(`${this.baseUrl}/exportar-leads-por-fecha`, {
      params,
      responseType: 'blob',
    });
  }

  /**
   * Exporta a Excel todos los leads filtrados por sede, supervisor y rango de fechas
   * @param sedeId ID de la sede (opcional)
   * @param supervisorId ID del supervisor (opcional)
   * @param fechaInicio Fecha de inicio del rango (formato YYYY-MM-DD)
   * @param fechaFin Fecha de fin del rango (formato YYYY-MM-DD)
   * @returns Observable con el archivo Excel como blob
   */
  exportarLeadsPorRangoFechas(
    sedeId: number | null,
    supervisorId: number | null,
    fechaInicio: string,
    fechaFin: string
  ): Observable<Blob> {
    let params = new HttpParams()
      .set('fechaInicio', fechaInicio)
      .set('fechaFin', fechaFin);

    if (sedeId !== null && sedeId !== undefined) {
      params = params.set('sedeId', sedeId.toString());
    }

    if (supervisorId !== null && supervisorId !== undefined) {
      params = params.set('supervisorId', supervisorId.toString());
    }

    console.log('Exportando por rango de fechas:', {
      sedeId,
      supervisorId,
      fechaInicio,
      fechaFin,
    });

    return this.http.get(`${this.baseUrl}/exportar-leads-por-rango-fechas`, {
      params,
      responseType: 'blob',
    });
  }
}
